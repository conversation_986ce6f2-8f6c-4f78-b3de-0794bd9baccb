import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation, useRoute } from '@react-navigation/native';

interface Community {
  _id: string;
  name: string;
  slug: string;
  description?: string;
  adminTrialInfo?: {
    activated: boolean;
    startDate?: string;
    endDate?: string;
  };
  paymentStatus?: string;
  freeTrialActivated?: boolean;
  subscriptionEndDate?: string;
  subscriptionId?: string;
  subscriptionStatus?: string;
}

interface Subscription {
  _id: string;
  planId: string;
  status: 'active' | 'cancelled' | 'expired' | 'pending';
  currentPeriodStart: string;
  currentPeriodEnd: string;
  amount: number;
  currency: string;
  interval: 'monthly' | 'yearly';
}

interface PaymentHistory {
  _id: string;
  amount: number;
  currency: string;
  status: 'success' | 'failed' | 'pending';
  createdAt: string;
  description: string;
}

const BillingScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { slug } = route.params as { slug: string };

  const [community, setCommunity] = useState<Community | null>(null);
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [paymentHistory, setPaymentHistory] = useState<PaymentHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [cancelling, setCancelling] = useState(false);

  useEffect(() => {
    fetchBillingData();
  }, [slug]);

  const fetchBillingData = async () => {
    try {
      const token = await AsyncStorage.getItem('auth_token');
      if (!token) {
        Alert.alert('Error', 'Please login again');
        return;
      }

      // Fetch community details
      const communityResponse = await fetch(`https://api.tribelab.com/api/community?slug=${slug}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (communityResponse.ok) {
        const communityData = await communityResponse.json();
        setCommunity(communityData);

        // Fetch subscription details
        const subscriptionResponse = await fetch(`https://api.tribelab.com/api/billing/${slug}/subscription`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (subscriptionResponse.ok) {
          const subscriptionData = await subscriptionResponse.json();
          setSubscription(subscriptionData);
        }

        // Fetch payment history
        const historyResponse = await fetch(`https://api.tribelab.com/api/billing/${slug}/history`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (historyResponse.ok) {
          const historyData = await historyResponse.json();
          setPaymentHistory(historyData.payments || []);
        }
      } else {
        throw new Error('Failed to fetch billing data');
      }
    } catch (error) {
      console.error('Error fetching billing data:', error);
      Alert.alert('Error', 'Failed to load billing information');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    fetchBillingData();
  };

  const cancelSubscription = async () => {
    Alert.alert(
      'Cancel Subscription',
      'Are you sure you want to cancel your subscription? This action cannot be undone.',
      [
        { text: 'Keep Subscription', style: 'cancel' },
        {
          text: 'Cancel Subscription',
          style: 'destructive',
          onPress: async () => {
            setCancelling(true);
            try {
              const token = await AsyncStorage.getItem('auth_token');
              const response = await fetch(`https://api.tribelab.com/api/billing/${slug}/cancel`, {
                method: 'POST',
                headers: {
                  'Authorization': `Bearer ${token}`,
                  'Content-Type': 'application/json',
                },
              });

              if (response.ok) {
                Alert.alert('Success', 'Subscription cancelled successfully');
                fetchBillingData(); // Refresh data
              } else {
                throw new Error('Failed to cancel subscription');
              }
            } catch (error) {
              console.error('Error cancelling subscription:', error);
              Alert.alert('Error', 'Failed to cancel subscription');
            } finally {
              setCancelling(false);
            }
          },
        },
      ]
    );
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatPrice = (amount: number, currency: string = 'INR') => {
    if (currency === 'INR') {
      return `₹${amount}`;
    }
    return `${currency} ${amount}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return '#28a745';
      case 'cancelled':
        return '#ff6b35';
      case 'expired':
        return '#dc3545';
      case 'pending':
        return '#ffc107';
      case 'success':
        return '#28a745';
      case 'failed':
        return '#dc3545';
      default:
        return '#6c757d';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return 'checkmark-circle';
      case 'cancelled':
        return 'close-circle';
      case 'expired':
        return 'time';
      case 'pending':
        return 'hourglass';
      case 'success':
        return 'checkmark-circle';
      case 'failed':
        return 'close-circle';
      default:
        return 'help-circle';
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Loading billing information...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Billing</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      >
        {/* Community Info */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Community</Text>
          <View style={styles.communityInfo}>
            <Text style={styles.communityName}>{community?.name}</Text>
            <Text style={styles.communitySlug}>@{community?.slug}</Text>
          </View>
        </View>

        {/* Subscription Status */}
        {subscription && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Current Subscription</Text>
            <View style={styles.subscriptionCard}>
              <View style={styles.subscriptionHeader}>
                <View style={styles.subscriptionInfo}>
                  <Text style={styles.subscriptionPlan}>
                    {subscription.interval === 'monthly' ? 'Monthly' : 'Yearly'} Plan
                  </Text>
                  <Text style={styles.subscriptionAmount}>
                    {formatPrice(subscription.amount, subscription.currency)}/{subscription.interval === 'monthly' ? 'month' : 'year'}
                  </Text>
                </View>
                <View style={[styles.statusBadge, { backgroundColor: getStatusColor(subscription.status) }]}>
                  <Ionicons name={getStatusIcon(subscription.status)} size={12} color="#fff" />
                  <Text style={styles.statusText}>
                    {subscription.status.charAt(0).toUpperCase() + subscription.status.slice(1)}
                  </Text>
                </View>
              </View>

              <View style={styles.subscriptionDetails}>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Current Period</Text>
                  <Text style={styles.detailValue}>
                    {formatDate(subscription.currentPeriodStart)} - {formatDate(subscription.currentPeriodEnd)}
                  </Text>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Next Billing</Text>
                  <Text style={styles.detailValue}>
                    {formatDate(subscription.currentPeriodEnd)}
                  </Text>
                </View>
              </View>

              {subscription.status === 'active' && (
                <TouchableOpacity
                  style={[styles.cancelButton, cancelling && styles.disabledButton]}
                  onPress={cancelSubscription}
                  disabled={cancelling}
                >
                  {cancelling ? (
                    <ActivityIndicator size="small" color="#fff" />
                  ) : (
                    <>
                      <Ionicons name="close-circle" size={16} color="#fff" />
                      <Text style={styles.cancelButtonText}>Cancel Subscription</Text>
                    </>
                  )}
                </TouchableOpacity>
              )}
            </View>
          </View>
        )}

        {/* Trial Information */}
        {community?.adminTrialInfo?.activated && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Free Trial</Text>
            <View style={styles.trialCard}>
              <View style={styles.trialHeader}>
                <Ionicons name="gift" size={24} color="#ffc107" />
                <Text style={styles.trialTitle}>Free Trial Active</Text>
              </View>
              <Text style={styles.trialText}>
                Your free trial is active until {formatDate(community.adminTrialInfo.endDate || '')}
              </Text>
            </View>
          </View>
        )}

        {/* Payment History */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Payment History</Text>
          {paymentHistory.length > 0 ? (
            paymentHistory.map((payment) => (
              <View key={payment._id} style={styles.paymentItem}>
                <View style={styles.paymentInfo}>
                  <Text style={styles.paymentDescription}>{payment.description}</Text>
                  <Text style={styles.paymentDate}>{formatDate(payment.createdAt)}</Text>
                </View>
                <View style={styles.paymentAmount}>
                  <Text style={styles.paymentPrice}>
                    {formatPrice(payment.amount, payment.currency)}
                  </Text>
                  <View style={[styles.paymentStatus, { backgroundColor: getStatusColor(payment.status) }]}>
                    <Ionicons name={getStatusIcon(payment.status)} size={12} color="#fff" />
                    <Text style={styles.paymentStatusText}>
                      {payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
                    </Text>
                  </View>
                </View>
              </View>
            ))
          ) : (
            <View style={styles.emptyContainer}>
              <Ionicons name="receipt" size={48} color="#ccc" />
              <Text style={styles.emptyText}>No payment history</Text>
            </View>
          )}
        </View>

        {/* Upgrade Options */}
        {!subscription && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Upgrade Your Community</Text>
            <TouchableOpacity style={styles.upgradeButton}>
              <Ionicons name="rocket" size={20} color="#fff" />
              <Text style={styles.upgradeButtonText}>Choose a Plan</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e1e5e9',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: '#fff',
    margin: 16,
    borderRadius: 12,
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 16,
  },
  communityInfo: {
    alignItems: 'center',
  },
  communityName: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1a1a1a',
    marginBottom: 4,
  },
  communitySlug: {
    fontSize: 14,
    color: '#666',
  },
  subscriptionCard: {
    borderWidth: 1,
    borderColor: '#e1e5e9',
    borderRadius: 8,
    padding: 16,
  },
  subscriptionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  subscriptionInfo: {
    flex: 1,
  },
  subscriptionPlan: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
  },
  subscriptionAmount: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  statusText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '600',
  },
  subscriptionDetails: {
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 14,
    color: '#666',
  },
  detailValue: {
    fontSize: 14,
    color: '#1a1a1a',
    fontWeight: '500',
  },
  cancelButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#dc3545',
    borderRadius: 8,
    paddingVertical: 12,
    gap: 8,
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  cancelButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  trialCard: {
    backgroundColor: '#fff9e6',
    borderWidth: 1,
    borderColor: '#ffc107',
    borderRadius: 8,
    padding: 16,
  },
  trialHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  trialTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
  },
  trialText: {
    fontSize: 14,
    color: '#666',
  },
  paymentItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  paymentInfo: {
    flex: 1,
  },
  paymentDescription: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1a1a1a',
  },
  paymentDate: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  paymentAmount: {
    alignItems: 'flex-end',
  },
  paymentPrice: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 4,
  },
  paymentStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    gap: 2,
  },
  paymentStatusText: {
    fontSize: 10,
    color: '#fff',
    fontWeight: '600',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
  },
  upgradeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#007AFF',
    borderRadius: 8,
    paddingVertical: 16,
    gap: 8,
  },
  upgradeButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default BillingScreen;
