import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
  Switch,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation } from '@react-navigation/native';

interface PaymentGateway {
  _id: string;
  name: string;
  isEnabled: boolean;
  credentials?: {
    [key: string]: string;
  };
  supportedCurrencies: string[];
  fees: {
    percentage: number;
    fixed: number;
  };
}

interface PaymentSettings {
  defaultCurrency: string;
  allowedCurrencies: string[];
  minimumAmount: number;
  maximumAmount: number;
  autoRefundEnabled: boolean;
  webhookUrl?: string;
}

const AdminPaymentSettingsScreen: React.FC = () => {
  const navigation = useNavigation();
  const [gateways, setGateways] = useState<PaymentGateway[]>([]);
  const [settings, setSettings] = useState<PaymentSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState<'gateways' | 'settings'>('gateways');

  useEffect(() => {
    fetchPaymentData();
  }, []);

  const fetchPaymentData = async () => {
    try {
      const token = await AsyncStorage.getItem('auth_token');
      if (!token) {
        Alert.alert('Error', 'Please login again');
        return;
      }

      // Fetch payment gateways
      const gatewaysResponse = await fetch('https://api.tribelab.com/api/admin/payment-gateways', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (gatewaysResponse.ok) {
        const gatewaysData = await gatewaysResponse.json();
        setGateways(gatewaysData.gateways || []);
      }

      // Fetch payment settings
      const settingsResponse = await fetch('https://api.tribelab.com/api/admin/payment-settings', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (settingsResponse.ok) {
        const settingsData = await settingsResponse.json();
        setSettings(settingsData);
      }
    } catch (error) {
      console.error('Error fetching payment data:', error);
      Alert.alert('Error', 'Failed to load payment settings');
    } finally {
      setLoading(false);
    }
  };

  const toggleGateway = async (gatewayId: string, enabled: boolean) => {
    try {
      const token = await AsyncStorage.getItem('auth_token');
      const response = await fetch(`https://api.tribelab.com/api/admin/payment-gateways/${gatewayId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isEnabled: enabled }),
      });

      if (response.ok) {
        setGateways(prev => prev.map(gateway => 
          gateway._id === gatewayId ? { ...gateway, isEnabled: enabled } : gateway
        ));
        Alert.alert('Success', `Gateway ${enabled ? 'enabled' : 'disabled'} successfully`);
      } else {
        throw new Error('Failed to update gateway');
      }
    } catch (error) {
      console.error('Error updating gateway:', error);
      Alert.alert('Error', 'Failed to update gateway');
    }
  };

  const saveSettings = async () => {
    if (!settings) return;

    setSaving(true);
    try {
      const token = await AsyncStorage.getItem('auth_token');
      const response = await fetch('https://api.tribelab.com/api/admin/payment-settings', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      });

      if (response.ok) {
        Alert.alert('Success', 'Payment settings saved successfully');
      } else {
        throw new Error('Failed to save settings');
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      Alert.alert('Error', 'Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  const configureGateway = (gateway: PaymentGateway) => {
    Alert.alert(
      'Configure Gateway',
      `Configure ${gateway.name} payment gateway`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Configure', 
          onPress: () => {
            // Navigate to gateway configuration screen
            navigation.navigate('GatewayConfig', { gatewayId: gateway._id });
          }
        },
      ]
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Loading payment settings...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Payment Settings</Text>
        <View style={styles.placeholder} />
      </View>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'gateways' && styles.activeTab]}
          onPress={() => setActiveTab('gateways')}
        >
          <Text style={[styles.tabText, activeTab === 'gateways' && styles.activeTabText]}>
            Gateways
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'settings' && styles.activeTab]}
          onPress={() => setActiveTab('settings')}
        >
          <Text style={[styles.tabText, activeTab === 'settings' && styles.activeTabText]}>
            Settings
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {activeTab === 'gateways' && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Payment Gateways</Text>
            <Text style={styles.sectionDescription}>
              Configure and manage payment gateways for your platform
            </Text>

            {gateways.map((gateway) => (
              <View key={gateway._id} style={styles.gatewayCard}>
                <View style={styles.gatewayHeader}>
                  <View style={styles.gatewayInfo}>
                    <Text style={styles.gatewayName}>{gateway.name}</Text>
                    <Text style={styles.gatewayFees}>
                      Fees: {gateway.fees.percentage}% + ₹{gateway.fees.fixed}
                    </Text>
                    <Text style={styles.gatewayCurrencies}>
                      Supports: {gateway.supportedCurrencies.join(', ')}
                    </Text>
                  </View>
                  <Switch
                    value={gateway.isEnabled}
                    onValueChange={(value) => toggleGateway(gateway._id, value)}
                    trackColor={{ false: '#e1e5e9', true: '#007AFF' }}
                    thumbColor="#fff"
                  />
                </View>

                {gateway.isEnabled && (
                  <TouchableOpacity
                    style={styles.configureButton}
                    onPress={() => configureGateway(gateway)}
                  >
                    <Ionicons name="settings" size={16} color="#007AFF" />
                    <Text style={styles.configureButtonText}>Configure</Text>
                  </TouchableOpacity>
                )}
              </View>
            ))}

            <TouchableOpacity style={styles.addGatewayButton}>
              <Ionicons name="add" size={20} color="#007AFF" />
              <Text style={styles.addGatewayText}>Add New Gateway</Text>
            </TouchableOpacity>
          </View>
        )}

        {activeTab === 'settings' && settings && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>General Settings</Text>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Default Currency</Text>
              <View style={styles.currencySelector}>
                {['INR', 'USD', 'EUR'].map((currency) => (
                  <TouchableOpacity
                    key={currency}
                    style={[
                      styles.currencyOption,
                      settings.defaultCurrency === currency && styles.activeCurrencyOption,
                    ]}
                    onPress={() => setSettings({ ...settings, defaultCurrency: currency })}
                  >
                    <Text style={[
                      styles.currencyOptionText,
                      settings.defaultCurrency === currency && styles.activeCurrencyOptionText,
                    ]}>
                      {currency}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Minimum Amount (₹)</Text>
              <TextInput
                style={styles.input}
                value={settings.minimumAmount.toString()}
                onChangeText={(text) => setSettings({ 
                  ...settings, 
                  minimumAmount: parseInt(text) || 0 
                })}
                keyboardType="numeric"
                placeholder="Minimum transaction amount"
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Maximum Amount (₹)</Text>
              <TextInput
                style={styles.input}
                value={settings.maximumAmount.toString()}
                onChangeText={(text) => setSettings({ 
                  ...settings, 
                  maximumAmount: parseInt(text) || 0 
                })}
                keyboardType="numeric"
                placeholder="Maximum transaction amount"
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Webhook URL</Text>
              <TextInput
                style={styles.input}
                value={settings.webhookUrl || ''}
                onChangeText={(text) => setSettings({ ...settings, webhookUrl: text })}
                placeholder="https://your-domain.com/webhook"
                autoCapitalize="none"
                keyboardType="url"
              />
              <Text style={styles.helperText}>
                URL to receive payment notifications
              </Text>
            </View>

            <View style={styles.switchGroup}>
              <View style={styles.switchInfo}>
                <Text style={styles.switchLabel}>Auto Refund</Text>
                <Text style={styles.switchDescription}>
                  Automatically process refunds for failed transactions
                </Text>
              </View>
              <Switch
                value={settings.autoRefundEnabled}
                onValueChange={(value) => setSettings({ ...settings, autoRefundEnabled: value })}
                trackColor={{ false: '#e1e5e9', true: '#007AFF' }}
                thumbColor="#fff"
              />
            </View>

            <TouchableOpacity
              style={[styles.saveButton, saving && styles.disabledButton]}
              onPress={saveSettings}
              disabled={saving}
            >
              {saving ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <Text style={styles.saveButtonText}>Save Settings</Text>
              )}
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e1e5e9',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
  },
  placeholder: {
    width: 40,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e1e5e9',
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#007AFF',
  },
  tabText: {
    fontSize: 16,
    color: '#666',
  },
  activeTabText: {
    color: '#007AFF',
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: '#fff',
    margin: 16,
    borderRadius: 12,
    padding: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 20,
    lineHeight: 20,
  },
  gatewayCard: {
    borderWidth: 1,
    borderColor: '#e1e5e9',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
  },
  gatewayHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  gatewayInfo: {
    flex: 1,
  },
  gatewayName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 4,
  },
  gatewayFees: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  gatewayCurrencies: {
    fontSize: 12,
    color: '#666',
  },
  configureButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderWidth: 1,
    borderColor: '#007AFF',
    borderRadius: 6,
    alignSelf: 'flex-start',
    gap: 4,
  },
  configureButtonText: {
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '500',
  },
  addGatewayButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderWidth: 1,
    borderColor: '#007AFF',
    borderRadius: 8,
    borderStyle: 'dashed',
    gap: 8,
  },
  addGatewayText: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '500',
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#e1e5e9',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  helperText: {
    marginTop: 4,
    fontSize: 12,
    color: '#666',
  },
  currencySelector: {
    flexDirection: 'row',
    gap: 8,
  },
  currencyOption: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: '#e1e5e9',
    borderRadius: 6,
    backgroundColor: '#fff',
  },
  activeCurrencyOption: {
    borderColor: '#007AFF',
    backgroundColor: '#f0f8ff',
  },
  currencyOptionText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  activeCurrencyOptionText: {
    color: '#007AFF',
  },
  switchGroup: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    marginBottom: 20,
  },
  switchInfo: {
    flex: 1,
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1a1a1a',
  },
  switchDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  saveButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default AdminPaymentSettingsScreen;
