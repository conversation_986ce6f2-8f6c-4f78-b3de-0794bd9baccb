import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
  Switch,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation } from '@react-navigation/native';

interface CommunityFormData {
  name: string;
  slug: string;
  description: string;
  isPrivate: boolean;
  pricingType: 'free' | 'paid';
  price: string;
  currency: string;
  adminQuestions: string[];
}

const CommunityCreateScreen: React.FC = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<CommunityFormData>({
    name: '',
    slug: '',
    description: '',
    isPrivate: false,
    pricingType: 'free',
    price: '',
    currency: 'INR',
    adminQuestions: [
      'Why do you want to join this community?',
      'What can you contribute to this community?',
    ],
  });

  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  // Auto-generate slug from name
  useEffect(() => {
    if (formData.name) {
      const slug = formData.name
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
      setFormData(prev => ({ ...prev, slug }));
    }
  }, [formData.name]);

  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Community name is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    if (formData.pricingType === 'paid') {
      if (!formData.price || parseFloat(formData.price) <= 0) {
        newErrors.price = 'Valid price is required for paid communities';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const createCommunity = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      const token = await AsyncStorage.getItem('auth_token');
      if (!token) {
        Alert.alert('Error', 'Please login again');
        return;
      }

      const payload = {
        ...formData,
        price: formData.pricingType === 'paid' ? parseFloat(formData.price) : 0,
      };

      const response = await fetch('https://api.tribelab.com/api/community', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (response.ok) {
        const data = await response.json();
        Alert.alert(
          'Success',
          'Community created successfully!',
          [
            {
              text: 'OK',
              onPress: () => {
                navigation.goBack();
                // Navigate to the new community
                // navigation.navigate('CommunityDetail', { slug: data.slug });
              },
            },
          ]
        );
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create community');
      }
    } catch (error) {
      console.error('Error creating community:', error);
      Alert.alert('Error', error.message || 'Failed to create community');
    } finally {
      setLoading(false);
    }
  };

  const addAdminQuestion = () => {
    setFormData(prev => ({
      ...prev,
      adminQuestions: [...prev.adminQuestions, ''],
    }));
  };

  const updateAdminQuestion = (index: number, value: string) => {
    const newQuestions = [...formData.adminQuestions];
    newQuestions[index] = value;
    setFormData(prev => ({ ...prev, adminQuestions: newQuestions }));
  };

  const removeAdminQuestion = (index: number) => {
    if (formData.adminQuestions.length > 1) {
      const newQuestions = formData.adminQuestions.filter((_, i) => i !== index);
      setFormData(prev => ({ ...prev, adminQuestions: newQuestions }));
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Create Community</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          {/* Community Name */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>Community Name *</Text>
            <TextInput
              style={[styles.input, errors.name && styles.inputError]}
              value={formData.name}
              onChangeText={(text) => setFormData(prev => ({ ...prev, name: text }))}
              placeholder="Enter community name"
              maxLength={100}
            />
            {errors.name && <Text style={styles.errorText}>{errors.name}</Text>}
          </View>

          {/* Slug */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>Community URL</Text>
            <View style={styles.slugContainer}>
              <Text style={styles.slugPrefix}>tribelab.com/</Text>
              <TextInput
                style={[styles.slugInput, errors.slug && styles.inputError]}
                value={formData.slug}
                onChangeText={(text) => setFormData(prev => ({ ...prev, slug: text }))}
                placeholder="community-url"
                autoCapitalize="none"
                maxLength={50}
              />
            </View>
            <Text style={styles.helperText}>This will be your community's web address</Text>
          </View>

          {/* Description */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>Description *</Text>
            <TextInput
              style={[styles.input, styles.textArea, errors.description && styles.inputError]}
              value={formData.description}
              onChangeText={(text) => setFormData(prev => ({ ...prev, description: text }))}
              placeholder="Describe your community..."
              multiline
              numberOfLines={4}
              maxLength={500}
            />
            {errors.description && <Text style={styles.errorText}>{errors.description}</Text>}
            <Text style={styles.helperText}>{formData.description.length}/500 characters</Text>
          </View>

          {/* Privacy Settings */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>Privacy Settings</Text>
            <View style={styles.switchContainer}>
              <View style={styles.switchRow}>
                <View style={styles.switchInfo}>
                  <Text style={styles.switchLabel}>Private Community</Text>
                  <Text style={styles.switchDescription}>
                    Members need approval to join
                  </Text>
                </View>
                <Switch
                  value={formData.isPrivate}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, isPrivate: value }))}
                  trackColor={{ false: '#e1e5e9', true: '#007AFF' }}
                  thumbColor="#fff"
                />
              </View>
            </View>
          </View>

          {/* Pricing */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>Pricing</Text>
            <View style={styles.pricingContainer}>
              <TouchableOpacity
                style={[
                  styles.pricingOption,
                  formData.pricingType === 'free' && styles.pricingOptionActive,
                ]}
                onPress={() => setFormData(prev => ({ ...prev, pricingType: 'free', price: '' }))}
              >
                <Ionicons
                  name={formData.pricingType === 'free' ? 'radio-button-on' : 'radio-button-off'}
                  size={20}
                  color={formData.pricingType === 'free' ? '#007AFF' : '#666'}
                />
                <Text style={styles.pricingText}>Free</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.pricingOption,
                  formData.pricingType === 'paid' && styles.pricingOptionActive,
                ]}
                onPress={() => setFormData(prev => ({ ...prev, pricingType: 'paid' }))}
              >
                <Ionicons
                  name={formData.pricingType === 'paid' ? 'radio-button-on' : 'radio-button-off'}
                  size={20}
                  color={formData.pricingType === 'paid' ? '#007AFF' : '#666'}
                />
                <Text style={styles.pricingText}>Paid</Text>
              </TouchableOpacity>
            </View>

            {formData.pricingType === 'paid' && (
              <View style={styles.priceInputContainer}>
                <View style={styles.currencyContainer}>
                  <Text style={styles.currencyText}>₹</Text>
                  <TextInput
                    style={[styles.priceInput, errors.price && styles.inputError]}
                    value={formData.price}
                    onChangeText={(text) => setFormData(prev => ({ ...prev, price: text }))}
                    placeholder="0"
                    keyboardType="numeric"
                  />
                  <Text style={styles.currencyText}>/month</Text>
                </View>
                {errors.price && <Text style={styles.errorText}>{errors.price}</Text>}
              </View>
            )}
          </View>

          {/* Admin Questions */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>Join Request Questions</Text>
            <Text style={styles.helperText}>
              Questions for members when they request to join (for private communities)
            </Text>
            
            {formData.adminQuestions.map((question, index) => (
              <View key={index} style={styles.questionContainer}>
                <TextInput
                  style={styles.input}
                  value={question}
                  onChangeText={(text) => updateAdminQuestion(index, text)}
                  placeholder={`Question ${index + 1}`}
                  multiline
                />
                {formData.adminQuestions.length > 1 && (
                  <TouchableOpacity
                    style={styles.removeButton}
                    onPress={() => removeAdminQuestion(index)}
                  >
                    <Ionicons name="close-circle" size={20} color="#ff4444" />
                  </TouchableOpacity>
                )}
              </View>
            ))}

            <TouchableOpacity style={styles.addQuestionButton} onPress={addAdminQuestion}>
              <Ionicons name="add" size={20} color="#007AFF" />
              <Text style={styles.addQuestionText}>Add Question</Text>
            </TouchableOpacity>
          </View>

          {/* Create Button */}
          <TouchableOpacity
            style={[styles.createButton, loading && styles.disabledButton]}
            onPress={createCommunity}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Text style={styles.createButtonText}>Create Community</Text>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e1e5e9',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: '#fff',
    margin: 16,
    borderRadius: 12,
    padding: 20,
  },
  formGroup: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#e1e5e9',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  inputError: {
    borderColor: '#ff4444',
  },
  errorText: {
    marginTop: 4,
    fontSize: 12,
    color: '#ff4444',
  },
  helperText: {
    marginTop: 4,
    fontSize: 12,
    color: '#666',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  slugContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e1e5e9',
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  slugPrefix: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#666',
    backgroundColor: '#f8f9fa',
    borderTopLeftRadius: 8,
    borderBottomLeftRadius: 8,
  },
  slugInput: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    borderWidth: 0,
  },
  switchContainer: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 16,
  },
  switchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  switchInfo: {
    flex: 1,
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1a1a1a',
  },
  switchDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  pricingContainer: {
    flexDirection: 'row',
    gap: 16,
  },
  pricingOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderWidth: 1,
    borderColor: '#e1e5e9',
    borderRadius: 8,
    backgroundColor: '#fff',
    flex: 1,
  },
  pricingOptionActive: {
    borderColor: '#007AFF',
    backgroundColor: '#f0f8ff',
  },
  pricingText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '500',
  },
  priceInputContainer: {
    marginTop: 16,
  },
  currencyContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e1e5e9',
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  currencyText: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#666',
  },
  priceInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
    borderWidth: 0,
    textAlign: 'center',
  },
  questionContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  removeButton: {
    marginLeft: 8,
    marginTop: 12,
  },
  addQuestionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderWidth: 1,
    borderColor: '#007AFF',
    borderRadius: 8,
    borderStyle: 'dashed',
  },
  addQuestionText: {
    marginLeft: 8,
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '500',
  },
  createButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 8,
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  createButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default CommunityCreateScreen;
