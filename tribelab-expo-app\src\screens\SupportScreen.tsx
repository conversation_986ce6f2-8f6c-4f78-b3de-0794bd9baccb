import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Linking,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation } from '@react-navigation/native';

interface FAQ {
  id: string;
  question: string;
  answer: string;
  category: string;
}

const SupportScreen: React.FC = () => {
  const navigation = useNavigation();
  const [activeTab, setActiveTab] = useState<'faq' | 'contact' | 'guides'>('faq');
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null);
  const [contactForm, setContactForm] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
  });
  const [submitting, setSubmitting] = useState(false);

  const faqs: FAQ[] = [
    {
      id: '1',
      question: 'How do I create a community?',
      answer: 'To create a community, go to the Communities tab and tap the "+" button. Fill in your community details including name, description, and privacy settings.',
      category: 'Getting Started',
    },
    {
      id: '2',
      question: 'How do I set up payments for my community?',
      answer: 'Go to your community settings, then navigate to Payment Settings. You can configure payment gateways like Razorpay, Stripe, or PayPal to accept payments from members.',
      category: 'Payments',
    },
    {
      id: '3',
      question: 'Can I create courses in my community?',
      answer: 'Yes! Community admins can create courses with multiple modules and lessons. Go to your community, tap on Courses, and then tap the "+" button to create a new course.',
      category: 'Courses',
    },
    {
      id: '4',
      question: 'How do I manage community members?',
      answer: 'As a community admin, you can view and manage members from the Members section. You can promote members to sub-admins, remove members, or approve join requests.',
      category: 'Community Management',
    },
    {
      id: '5',
      question: 'What payment methods are supported?',
      answer: 'We support all major payment methods including credit/debit cards, UPI, net banking, and digital wallets through our integrated payment gateways.',
      category: 'Payments',
    },
    {
      id: '6',
      question: 'How do I cancel my subscription?',
      answer: 'You can cancel your subscription from the Billing section in your community settings. Your access will continue until the end of your current billing period.',
      category: 'Billing',
    },
  ];

  const guides = [
    {
      id: '1',
      title: 'Getting Started Guide',
      description: 'Learn the basics of using TribeLab',
      icon: 'rocket',
      color: '#007AFF',
    },
    {
      id: '2',
      title: 'Community Admin Guide',
      description: 'Complete guide for community administrators',
      icon: 'shield-checkmark',
      color: '#28a745',
    },
    {
      id: '3',
      title: 'Payment Setup Guide',
      description: 'Set up payments for your community',
      icon: 'card',
      color: '#ffc107',
    },
    {
      id: '4',
      title: 'Course Creation Guide',
      description: 'Create engaging courses for your members',
      icon: 'school',
      color: '#ff6b35',
    },
  ];

  const submitContactForm = async () => {
    if (!contactForm.name || !contactForm.email || !contactForm.subject || !contactForm.message) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    setSubmitting(true);
    try {
      const token = await AsyncStorage.getItem('auth_token');
      const response = await fetch('https://api.tribelab.com/api/support/contact', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(contactForm),
      });

      if (response.ok) {
        Alert.alert('Success', 'Your message has been sent. We\'ll get back to you soon!');
        setContactForm({ name: '', email: '', subject: '', message: '' });
      } else {
        throw new Error('Failed to send message');
      }
    } catch (error) {
      console.error('Error submitting contact form:', error);
      Alert.alert('Error', 'Failed to send message. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const openExternalLink = (url: string) => {
    Linking.openURL(url).catch(() => {
      Alert.alert('Error', 'Could not open link');
    });
  };

  const renderFAQ = (faq: FAQ) => (
    <View key={faq.id} style={styles.faqItem}>
      <TouchableOpacity
        style={styles.faqQuestion}
        onPress={() => setExpandedFAQ(expandedFAQ === faq.id ? null : faq.id)}
      >
        <Text style={styles.faqQuestionText}>{faq.question}</Text>
        <Ionicons
          name={expandedFAQ === faq.id ? 'chevron-up' : 'chevron-down'}
          size={20}
          color="#666"
        />
      </TouchableOpacity>
      {expandedFAQ === faq.id && (
        <View style={styles.faqAnswer}>
          <Text style={styles.faqAnswerText}>{faq.answer}</Text>
        </View>
      )}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Support</Text>
        <View style={styles.placeholder} />
      </View>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'faq' && styles.activeTab]}
          onPress={() => setActiveTab('faq')}
        >
          <Ionicons name="help-circle" size={20} color={activeTab === 'faq' ? '#007AFF' : '#666'} />
          <Text style={[styles.tabText, activeTab === 'faq' && styles.activeTabText]}>FAQ</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'guides' && styles.activeTab]}
          onPress={() => setActiveTab('guides')}
        >
          <Ionicons name="book" size={20} color={activeTab === 'guides' ? '#007AFF' : '#666'} />
          <Text style={[styles.tabText, activeTab === 'guides' && styles.activeTabText]}>Guides</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'contact' && styles.activeTab]}
          onPress={() => setActiveTab('contact')}
        >
          <Ionicons name="mail" size={20} color={activeTab === 'contact' ? '#007AFF' : '#666'} />
          <Text style={[styles.tabText, activeTab === 'contact' && styles.activeTabText]}>Contact</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {activeTab === 'faq' && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Frequently Asked Questions</Text>
            <Text style={styles.sectionDescription}>
              Find answers to common questions about TribeLab
            </Text>

            {faqs.map(renderFAQ)}

            <View style={styles.contactPrompt}>
              <Text style={styles.contactPromptText}>
                Can't find what you're looking for?
              </Text>
              <TouchableOpacity
                style={styles.contactPromptButton}
                onPress={() => setActiveTab('contact')}
              >
                <Text style={styles.contactPromptButtonText}>Contact Support</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {activeTab === 'guides' && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Help Guides</Text>
            <Text style={styles.sectionDescription}>
              Step-by-step guides to help you get the most out of TribeLab
            </Text>

            {guides.map((guide) => (
              <TouchableOpacity key={guide.id} style={styles.guideCard}>
                <View style={[styles.guideIcon, { backgroundColor: guide.color }]}>
                  <Ionicons name={guide.icon as any} size={24} color="#fff" />
                </View>
                <View style={styles.guideContent}>
                  <Text style={styles.guideTitle}>{guide.title}</Text>
                  <Text style={styles.guideDescription}>{guide.description}</Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color="#666" />
              </TouchableOpacity>
            ))}

            <View style={styles.externalLinks}>
              <Text style={styles.externalLinksTitle}>Additional Resources</Text>
              
              <TouchableOpacity
                style={styles.externalLink}
                onPress={() => openExternalLink('https://docs.tribelab.com')}
              >
                <Ionicons name="document-text" size={20} color="#007AFF" />
                <Text style={styles.externalLinkText}>Documentation</Text>
                <Ionicons name="open" size={16} color="#666" />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.externalLink}
                onPress={() => openExternalLink('https://www.youtube.com/@tribelab')}
              >
                <Ionicons name="logo-youtube" size={20} color="#ff0000" />
                <Text style={styles.externalLinkText}>Video Tutorials</Text>
                <Ionicons name="open" size={16} color="#666" />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.externalLink}
                onPress={() => openExternalLink('https://community.tribelab.com')}
              >
                <Ionicons name="people" size={20} color="#28a745" />
                <Text style={styles.externalLinkText}>Community Forum</Text>
                <Ionicons name="open" size={16} color="#666" />
              </TouchableOpacity>
            </View>
          </View>
        )}

        {activeTab === 'contact' && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Contact Support</Text>
            <Text style={styles.sectionDescription}>
              Get in touch with our support team for personalized help
            </Text>

            <View style={styles.contactMethods}>
              <TouchableOpacity
                style={styles.contactMethod}
                onPress={() => Linking.openURL('mailto:<EMAIL>')}
              >
                <Ionicons name="mail" size={24} color="#007AFF" />
                <View style={styles.contactMethodContent}>
                  <Text style={styles.contactMethodTitle}>Email Support</Text>
                  <Text style={styles.contactMethodDescription}><EMAIL></Text>
                </View>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.contactMethod}
                onPress={() => openExternalLink('https://wa.me/919876543210')}
              >
                <Ionicons name="logo-whatsapp" size={24} color="#25D366" />
                <View style={styles.contactMethodContent}>
                  <Text style={styles.contactMethodTitle}>WhatsApp</Text>
                  <Text style={styles.contactMethodDescription}>+91 98765 43210</Text>
                </View>
              </TouchableOpacity>
            </View>

            <View style={styles.contactForm}>
              <Text style={styles.formTitle}>Send us a message</Text>

              <View style={styles.formGroup}>
                <Text style={styles.label}>Name</Text>
                <TextInput
                  style={styles.input}
                  value={contactForm.name}
                  onChangeText={(text) => setContactForm({ ...contactForm, name: text })}
                  placeholder="Your name"
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.label}>Email</Text>
                <TextInput
                  style={styles.input}
                  value={contactForm.email}
                  onChangeText={(text) => setContactForm({ ...contactForm, email: text })}
                  placeholder="<EMAIL>"
                  keyboardType="email-address"
                  autoCapitalize="none"
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.label}>Subject</Text>
                <TextInput
                  style={styles.input}
                  value={contactForm.subject}
                  onChangeText={(text) => setContactForm({ ...contactForm, subject: text })}
                  placeholder="What can we help you with?"
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.label}>Message</Text>
                <TextInput
                  style={[styles.input, styles.textArea]}
                  value={contactForm.message}
                  onChangeText={(text) => setContactForm({ ...contactForm, message: text })}
                  placeholder="Describe your issue or question..."
                  multiline
                  numberOfLines={4}
                />
              </View>

              <TouchableOpacity
                style={[styles.submitButton, submitting && styles.disabledButton]}
                onPress={submitContactForm}
                disabled={submitting}
              >
                {submitting ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <>
                    <Ionicons name="send" size={16} color="#fff" />
                    <Text style={styles.submitButtonText}>Send Message</Text>
                  </>
                )}
              </TouchableOpacity>
            </View>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e1e5e9',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
  },
  placeholder: {
    width: 40,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e1e5e9',
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    gap: 8,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#007AFF',
  },
  tabText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  activeTabText: {
    color: '#007AFF',
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: '#fff',
    margin: 16,
    borderRadius: 12,
    padding: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 20,
    lineHeight: 20,
  },
  faqItem: {
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    marginBottom: 12,
  },
  faqQuestion: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  faqQuestionText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1a1a1a',
    flex: 1,
    marginRight: 8,
  },
  faqAnswer: {
    paddingBottom: 12,
  },
  faqAnswerText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  contactPrompt: {
    alignItems: 'center',
    paddingVertical: 20,
    marginTop: 20,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  contactPromptText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 12,
  },
  contactPromptButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  contactPromptButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  guideCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderWidth: 1,
    borderColor: '#e1e5e9',
    borderRadius: 8,
    marginBottom: 12,
  },
  guideIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  guideContent: {
    flex: 1,
  },
  guideTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 4,
  },
  guideDescription: {
    fontSize: 14,
    color: '#666',
  },
  externalLinks: {
    marginTop: 20,
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  externalLinksTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 12,
  },
  externalLink: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    gap: 12,
  },
  externalLinkText: {
    fontSize: 14,
    color: '#1a1a1a',
    flex: 1,
  },
  contactMethods: {
    marginBottom: 24,
  },
  contactMethod: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderWidth: 1,
    borderColor: '#e1e5e9',
    borderRadius: 8,
    marginBottom: 12,
  },
  contactMethodContent: {
    marginLeft: 16,
  },
  contactMethodTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
  },
  contactMethodDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  contactForm: {
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  formTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1a1a1a',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#e1e5e9',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  submitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#007AFF',
    borderRadius: 8,
    paddingVertical: 16,
    gap: 8,
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default SupportScreen;
