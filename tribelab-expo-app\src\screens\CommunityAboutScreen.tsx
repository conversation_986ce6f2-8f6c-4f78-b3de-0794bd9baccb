import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation, useRoute } from '@react-navigation/native';

interface Community {
  _id: string;
  name: string;
  slug: string;
  description: string;
  iconImageUrl?: string;
  bannerImageurl?: string;
  members: string[];
  admin: string;
  subAdmins?: string[];
  isPrivate: boolean;
  pricingType: 'free' | 'paid';
  price?: number;
  currency?: string;
  createdAt: string;
  createdBy: string;
}

interface CommunityStats {
  totalMembers: number;
  totalPosts: number;
  totalCourses: number;
  joinedDate?: string;
}

const CommunityAboutScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { slug } = route.params as { slug: string };

  const [community, setCommunity] = useState<Community | null>(null);
  const [stats, setStats] = useState<CommunityStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [userRole, setUserRole] = useState<'admin' | 'sub-admin' | 'member' | 'non-member'>('non-member');

  useEffect(() => {
    fetchCommunityDetails();
  }, [slug]);

  const fetchCommunityDetails = async () => {
    try {
      const token = await AsyncStorage.getItem('auth_token');
      if (!token) {
        Alert.alert('Error', 'Please login again');
        return;
      }

      // Fetch community details
      const communityResponse = await fetch(`https://api.tribelab.com/api/community?slug=${slug}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (communityResponse.ok) {
        const communityData = await communityResponse.json();
        setCommunity(communityData);

        // Determine user role
        const userId = await AsyncStorage.getItem('user_id');
        if (userId) {
          if (communityData.admin === userId) {
            setUserRole('admin');
          } else if (communityData.subAdmins?.includes(userId)) {
            setUserRole('sub-admin');
          } else if (communityData.members.includes(userId)) {
            setUserRole('member');
          } else {
            setUserRole('non-member');
          }
        }

        // Fetch community stats
        const statsResponse = await fetch(`https://api.tribelab.com/api/community/${communityData._id}/stats`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (statsResponse.ok) {
          const statsData = await statsResponse.json();
          setStats(statsData);
        }
      } else {
        throw new Error('Failed to fetch community details');
      }
    } catch (error) {
      console.error('Error fetching community:', error);
      Alert.alert('Error', 'Failed to load community details');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatPrice = (price: number, currency: string = 'INR') => {
    if (currency === 'INR') {
      return `₹${price}`;
    }
    return `${currency} ${price}`;
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Loading community details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!community) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={48} color="#ff4444" />
          <Text style={styles.errorText}>Community not found</Text>
          <TouchableOpacity style={styles.retryButton} onPress={fetchCommunityDetails}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>About</Text>
        {(userRole === 'admin' || userRole === 'sub-admin') && (
          <TouchableOpacity
            style={styles.editButton}
            onPress={() => navigation.navigate('CommunitySettings', { slug })}
          >
            <Ionicons name="settings" size={24} color="#007AFF" />
          </TouchableOpacity>
        )}
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Banner Image */}
        {community.bannerImageurl && (
          <Image source={{ uri: community.bannerImageurl }} style={styles.bannerImage} />
        )}

        <View style={styles.section}>
          {/* Community Header */}
          <View style={styles.communityHeader}>
            {community.iconImageUrl && (
              <Image source={{ uri: community.iconImageUrl }} style={styles.communityIcon} />
            )}
            <View style={styles.communityInfo}>
              <Text style={styles.communityName}>{community.name}</Text>
              <Text style={styles.communitySlug}>@{community.slug}</Text>
              <View style={styles.badges}>
                <View style={[styles.badge, community.isPrivate ? styles.privateBadge : styles.publicBadge]}>
                  <Ionicons
                    name={community.isPrivate ? 'lock-closed' : 'globe'}
                    size={12}
                    color="#fff"
                  />
                  <Text style={styles.badgeText}>
                    {community.isPrivate ? 'Private' : 'Public'}
                  </Text>
                </View>
                <View style={[styles.badge, community.pricingType === 'paid' ? styles.paidBadge : styles.freeBadge]}>
                  <Ionicons
                    name={community.pricingType === 'paid' ? 'card' : 'gift'}
                    size={12}
                    color="#fff"
                  />
                  <Text style={styles.badgeText}>
                    {community.pricingType === 'paid' 
                      ? formatPrice(community.price || 0, community.currency)
                      : 'Free'
                    }
                  </Text>
                </View>
              </View>
            </View>
          </View>

          {/* Description */}
          <View style={styles.descriptionContainer}>
            <Text style={styles.sectionTitle}>Description</Text>
            <Text style={styles.description}>{community.description}</Text>
          </View>

          {/* Stats */}
          {stats && (
            <View style={styles.statsContainer}>
              <Text style={styles.sectionTitle}>Community Stats</Text>
              <View style={styles.statsGrid}>
                <View style={styles.statItem}>
                  <Ionicons name="people" size={24} color="#007AFF" />
                  <Text style={styles.statNumber}>{stats.totalMembers}</Text>
                  <Text style={styles.statLabel}>Members</Text>
                </View>
                <View style={styles.statItem}>
                  <Ionicons name="chatbubbles" size={24} color="#28a745" />
                  <Text style={styles.statNumber}>{stats.totalPosts}</Text>
                  <Text style={styles.statLabel}>Posts</Text>
                </View>
                <View style={styles.statItem}>
                  <Ionicons name="school" size={24} color="#ffc107" />
                  <Text style={styles.statNumber}>{stats.totalCourses}</Text>
                  <Text style={styles.statLabel}>Courses</Text>
                </View>
              </View>
            </View>
          )}

          {/* Community Info */}
          <View style={styles.infoContainer}>
            <Text style={styles.sectionTitle}>Community Information</Text>
            
            <View style={styles.infoItem}>
              <Ionicons name="calendar" size={20} color="#666" />
              <View style={styles.infoText}>
                <Text style={styles.infoLabel}>Created</Text>
                <Text style={styles.infoValue}>{formatDate(community.createdAt)}</Text>
              </View>
            </View>

            {stats?.joinedDate && (
              <View style={styles.infoItem}>
                <Ionicons name="person-add" size={20} color="#666" />
                <View style={styles.infoText}>
                  <Text style={styles.infoLabel}>You joined</Text>
                  <Text style={styles.infoValue}>{formatDate(stats.joinedDate)}</Text>
                </View>
              </View>
            )}

            <View style={styles.infoItem}>
              <Ionicons name="shield-checkmark" size={20} color="#666" />
              <View style={styles.infoText}>
                <Text style={styles.infoLabel}>Your role</Text>
                <Text style={styles.infoValue}>
                  {userRole === 'admin' ? 'Administrator' :
                   userRole === 'sub-admin' ? 'Sub Administrator' :
                   userRole === 'member' ? 'Member' : 'Not a member'}
                </Text>
              </View>
            </View>
          </View>

          {/* Action Buttons */}
          <View style={styles.actionContainer}>
            {userRole === 'non-member' && (
              <TouchableOpacity style={styles.joinButton}>
                <Text style={styles.joinButtonText}>
                  {community.pricingType === 'paid' ? `Join for ${formatPrice(community.price || 0, community.currency)}` : 'Join Community'}
                </Text>
              </TouchableOpacity>
            )}

            {(userRole === 'admin' || userRole === 'sub-admin') && (
              <TouchableOpacity
                style={styles.manageButton}
                onPress={() => navigation.navigate('CommunitySettings', { slug })}
              >
                <Ionicons name="settings" size={20} color="#fff" />
                <Text style={styles.manageButtonText}>Manage Community</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: '#ff4444',
    marginTop: 16,
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e1e5e9',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
  },
  editButton: {
    padding: 8,
  },
  content: {
    flex: 1,
  },
  bannerImage: {
    width: '100%',
    height: 200,
    resizeMode: 'cover',
  },
  section: {
    backgroundColor: '#fff',
    margin: 16,
    borderRadius: 12,
    padding: 20,
  },
  communityHeader: {
    flexDirection: 'row',
    marginBottom: 24,
  },
  communityIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 16,
  },
  communityInfo: {
    flex: 1,
  },
  communityName: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1a1a1a',
    marginBottom: 4,
  },
  communitySlug: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  badges: {
    flexDirection: 'row',
    gap: 8,
  },
  badge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  privateBadge: {
    backgroundColor: '#ff6b35',
  },
  publicBadge: {
    backgroundColor: '#28a745',
  },
  paidBadge: {
    backgroundColor: '#007AFF',
  },
  freeBadge: {
    backgroundColor: '#6c757d',
  },
  badgeText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '600',
  },
  descriptionContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
  },
  statsContainer: {
    marginBottom: 24,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1a1a1a',
    marginTop: 8,
  },
  statLabel: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  infoContainer: {
    marginBottom: 24,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  infoText: {
    marginLeft: 12,
    flex: 1,
  },
  infoLabel: {
    fontSize: 14,
    color: '#666',
  },
  infoValue: {
    fontSize: 16,
    color: '#1a1a1a',
    fontWeight: '500',
    marginTop: 2,
  },
  actionContainer: {
    gap: 12,
  },
  joinButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
  },
  joinButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  manageButton: {
    backgroundColor: '#28a745',
    borderRadius: 8,
    paddingVertical: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  manageButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default CommunityAboutScreen;
